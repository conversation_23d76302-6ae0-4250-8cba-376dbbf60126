<?php

namespace App\Repositories\Contracts;

use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\API\Taisan\Congdap;

interface CongdapRepositoryInterface
{
    public function paginate(array $filters = [], int $perPage = 15): LengthAwarePaginator;
    public function paginateGeometryOnly(array $filters = []): LengthAwarePaginator;
    public function paginateAttributesOnly(array $filters = [], int $perPage = 15): LengthAwarePaginator;
    public function findById(string $id): ?Congdap;
    public function create(array $data): Congdap;
    public function update(string $id, array $data): ?Congdap;
    public function delete(string $id): bool;
    public function checkExists(string $id): bool;
}