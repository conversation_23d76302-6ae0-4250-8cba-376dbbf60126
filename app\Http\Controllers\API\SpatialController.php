<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SpatialController extends Controller
{
    use ApiResponseTrait;

    /**
     * Tìm xã dựa trên tọa độ (point-in-polygon query)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function findXaByCoordinates(Request $request)
    {
        try {
            // Validate input
            $validator = Validator::make($request->all(), [
                'longitude' => 'required|numeric|between:-180,180',
                'latitude' => 'required|numeric|between:-90,90'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Dữ liệu tọa độ không hợp lệ: ' . $validator->errors()->first(),
                    400
                );
            }

            $longitude = $request->input('longitude');
            $latitude = $request->input('latitude');

            // Thực hiện spatial query để tìm xã chứa điểm
            $xa = DB::connection('pgsql')
                ->table('basemap.rg_xa')
                ->selectRaw('id, tenxa, id_huyen, tenhuyen')
                ->whereRaw('ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), 4326))', [$longitude, $latitude])
                ->first();

            if (!$xa) {
                return $this->successResponse(
                    data: null,
                    message: 'Không tìm thấy xã tại tọa độ này'
                );
            }

            return $this->successResponse(
                data: [
                    'id_xa' => $xa->id,
                    'ten_xa' => $xa->tenxa,
                    'id_huyen' => $xa->id_huyen,
                    'ten_huyen' => $xa->tenhuyen,
                    'coordinates' => [
                        'longitude' => $longitude,
                        'latitude' => $latitude
                    ]
                ],
                message: 'Tìm thấy thông tin xã thành công'
            );

        } catch (\Exception $e) {
            Log::error('Error in spatial query: ' . $e->getMessage(), [
                'longitude' => $request->input('longitude'),
                'latitude' => $request->input('latitude'),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Lỗi khi thực hiện truy vấn không gian: ' . $e->getMessage(),
                500
            );
        }
    }
}
