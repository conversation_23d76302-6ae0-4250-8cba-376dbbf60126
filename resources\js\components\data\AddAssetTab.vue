<template>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
    <h2 class="text-xl font-semibold mb-4">Thêm Mới Tài <PERSON>n</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <label class="block text-sm font-medium text-gray-700">Tên công trình *</label>
        <input type="text" v-model="formData.ten" placeholder="Nhập tên công trình" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Quy mô công trình</label>
        <input type="text" v-model="formData.quymo_ct" placeholder="Nhập quy mô công trình" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Loại công trình</label>
        <input type="text" v-model="formData.loai_ct" placeholder="Nhập loại công trình" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Đơn vị quản lý</label>
        <input type="text" v-model="formData.dv_quanly" placeholder="Nhập đơn vị quản lý" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Tình trạng</label>
        <input type="text" v-model="formData.tinhtrang" placeholder="Nhập tình trạng" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Năm xây dựng</label>
        <input type="number" v-model="formData.nam_xd" placeholder="Nhập năm xây dựng" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Tỉnh/Thành phố *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn tỉnh/thành phố</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Quận/Huyện *</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn quận/huyện</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700">Xã/Phường</label>
        <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
          <option>Chọn xã/phường</option>
        </select>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Địa chỉ chi tiết</label>
        <input type="text" placeholder="Nhập địa chỉ chi tiết" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
      </div>
      <div class="md:col-span-3 grid grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">Kinh độ (Longitude)</label>
          <input type="number" step="any" v-model="formData.longitude" placeholder="Kinh độ (Longitude)" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">Vĩ độ (Latitude)</label>
          <input type="number" step="any" v-model="formData.latitude" placeholder="Vĩ độ (Latitude)" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" />
        </div>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Chú thích</label>
        <textarea v-model="formData.chuthich" placeholder="Nhập chú thích" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
      </div>
      <div class="md:col-span-3">
        <label class="block text-sm font-medium text-gray-700">Tài liệu đính kèm</label>
        <div class="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
          <div class="space-y-1 text-center">
            <Icon name="upload-cloud" class="mx-auto h-12 w-12 text-gray-400" />
            <div class="flex text-sm text-gray-600">
              <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                <span>Tải lên tài liệu</span>
                <input type="file" class="sr-only" />
              </label>
              <p class="pl-1">hoặc kéo thả vào đây</p>
            </div>
            <p class="text-xs text-gray-500">PNG, JPG, PDF tối đa 10MB</p>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-6 flex justify-end gap-3">
      <button @click="resetForm" :disabled="isSubmitting" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50">
        Đặt lại
      </button>
      <button @click="submitForm" :disabled="isSubmitting" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50">
        {{ isSubmitting ? 'Đang xử lý...' : 'Thêm mới' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Icon from '@/components/Icon.vue'
import axios from 'axios'

// Define Emits để thông báo cho parent component
const emit = defineEmits(['refresh-data'])

// Form data
const formData = ref({
  ten: '',
  quymo_ct: '',
  loai_ct: '',
  nam_xd: '',
  nam_sd: '',
  dt_dat: '',
  tinhtrang: '',
  quytrinh_vh: '',
  quytrinh_bt: '',
  dv_quanly: '',
  phuongthuc: '',
  chuthich: '',
  longitude: '',
  latitude: ''
})

const isSubmitting = ref(false)

// Reset form
const resetForm = () => {
  Object.keys(formData.value).forEach(key => {
    (formData.value as any)[key] = ''
  })
}

// Submit form
const submitForm = async () => {
  try {
    isSubmitting.value = true

    // Validate required fields
    if (!formData.value.ten) {
      alert('Vui lòng nhập tên công trình')
      return
    }

    // Prepare data for API
    const apiData: any = { ...formData.value }

    // Add geometry if coordinates are provided
    if (formData.value.longitude && formData.value.latitude) {
      apiData.geometry = {
        type: 'Point',
        coordinates: [parseFloat(formData.value.longitude), parseFloat(formData.value.latitude)]
      }
    }

    // Remove coordinate fields from API data
    delete apiData.longitude
    delete apiData.latitude

    const response = await axios.post('/api/taisan/congdap', apiData)

    if (response.data.success) {
      alert('Thêm mới thành công!')
      resetForm()
      // Trigger refresh data
      emit('refresh-data')
    } else {
      throw new Error(response.data.message || 'Thêm mới thất bại')
    }
  } catch (error) {
    console.error('Lỗi khi thêm mới:', error)
    alert('Có lỗi xảy ra khi thêm mới. Vui lòng thử lại.')
  } finally {
    isSubmitting.value = false
  }
}
</script>
